#!/bin/bash

# Script to build and push Docker image to Azure Container Registry
# Usage: ./build-and-push-acr.sh <ACR_NAME> <ACR_USERNAME> <ACR_PASSWORD> [BUILD_ENV] [IMAGE_TAG]

set -e  # Exit on any error

# Check if required parameters are provided
if [ $# -lt 3 ]; then
    echo "Usage: $0 <ACR_NAME> <ACR_USERNAME> <ACR_PASSWORD> [BUILD_ENV] [IMAGE_TAG]"
    echo ""
    echo "Parameters:"
    echo "  ACR_NAME      - Azure Container Registry name (without .azurecr.io)"
    echo "  ACR_USERNAME  - ACR username"
    echo "  ACR_PASSWORD  - ACR password"
    echo "  BUILD_ENV     - Build environment (default: staging)"
    echo "  IMAGE_TAG     - Image tag (default: latest-staging)"
    echo ""
    echo "Example:"
    echo "  $0 myacr myusername mypassword staging latest-staging"
    exit 1
fi

# Parse parameters
ACR_NAME="$1"
ACR_USERNAME="$2"
ACR_PASSWORD="$3"
BUILD_ENV="${4:-staging}"
IMAGE_TAG="${5:-latest-staging}"

# Derived variables
ACR_URL="${ACR_NAME}.azurecr.io"
IMAGE_NAME="aibase"
FULL_IMAGE_TAG="${ACR_URL}/${IMAGE_NAME}:${IMAGE_TAG}"

echo "=== Docker Build and Push to ACR ==="
echo "ACR URL: ${ACR_URL}"
echo "Image: ${FULL_IMAGE_TAG}"
echo "Build Environment: ${BUILD_ENV}"
echo "Platform: linux/amd64"
echo ""

# Set up Docker Buildx for multi-platform builds
echo "Setting up Docker Buildx..."
docker buildx create --use --name acr-builder --driver docker-container 2>/dev/null || docker buildx use acr-builder

# Log in to ACR
echo "Logging in to Azure Container Registry..."
echo "${ACR_PASSWORD}" | docker login "${ACR_URL}" -u "${ACR_USERNAME}" --password-stdin

# Build and push Docker image for linux/amd64 platform
echo "Building and pushing Docker image..."
docker buildx build \
    --platform linux/amd64 \
    --build-arg BUILD_ENV="${BUILD_ENV}" \
    --tag "${FULL_IMAGE_TAG}" \
    --push \
    .

# Log out from Docker
echo "Logging out from Docker..."
docker logout "${ACR_URL}"

# Clean up builder (optional)
echo "Cleaning up..."
docker buildx rm acr-builder 2>/dev/null || true

echo ""
echo "✅ Successfully built and pushed image: ${FULL_IMAGE_TAG}"
echo "Platform: linux/amd64"
echo "Build Environment: ${BUILD_ENV}"
